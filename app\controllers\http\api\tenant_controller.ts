import type { HttpContext } from '@adonisjs/core/http'
import TenantOnboardingService from '#services/tenant_onboarding_service'
import Tenant from '#models/tenant_model'
import { createTenantValidator, updateTenantValidator } from '#validators/tenant_validator'

export default class TenantController {
  /**
   * @create
   * @summary Create new tenant
   * @description Create a new tenant with automatic database provisioning (superadmin only)
   * @tag Tenant Management
   * @requestBody {"name": "Acme Corp", "slug": "acme-corp", "dbHost": "localhost", "dbPort": 5432, "dbName": "acme_corp_db", "dbUser": "acme_user", "dbPassword": "secure_password", "status": "active"}
   * @responseBody 201 - {"message": "Tenant created successfully", "data": {"tenant": {"id": "uuid", "name": "Acme Corp", "slug": "acme-corp", "dbHost": "localhost", "dbPort": 5432, "dbName": "acme_corp_db", "dbUser": "acme_user", "status": "active"}}}
   * @responseBody 403 - {"message": "Only superadmin can create tenants"}
   * @responseBody 422 - {"message": "Validation failed", "errors": [{"field": "slug", "message": "Slug already exists"}]}
   * @responseBody 400 - {"message": "Failed to create tenant", "error": "Database provisioning failed"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async create({ request, auth, response }: HttpContext) {
    try {
      // Only superadmin can create tenants
      const currentUser = auth.getUserOrFail()
      if (currentUser.role !== 'superadmin') {
        return response.forbidden({
          message: 'Only superadmin can create tenants'
        })
      }

      // Validate the request data
      const tenantData = await request.validateUsing(createTenantValidator)

      // Provision the tenant (creates database, user, runs migrations)
      const tenant = await TenantOnboardingService.provisionTenant(tenantData)

      return response.created({
        message: 'Tenant created successfully',
        data: {
          tenant: {
            id: tenant.id,
            name: tenant.name,
            slug: tenant.slug,
            dbHost: tenant.dbHost,
            dbPort: tenant.dbPort,
            dbName: tenant.dbName,
            dbUser: tenant.dbUser,
            // Don't return the password for security
            status: tenant.status
          }
        }
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to create tenant',
        error: error.message
      })
    }
  }

  /**
   * @index
   * @summary List all tenants
   * @description Retrieve a list of all tenants (superadmin and admin only)
   * @tag Tenant Management
   * @responseBody 200 - {"message": "Tenants retrieved successfully", "data": {"tenants": [{"id": "uuid", "name": "Acme Corp", "slug": "acme-corp", "dbHost": "localhost", "dbPort": 5432, "dbName": "acme_corp_db", "dbUser": "acme_user", "status": "active"}]}}
   * @responseBody 403 - {"message": "Insufficient permissions to list tenants"}
   * @responseBody 400 - {"message": "Failed to retrieve tenants", "error": "Database error"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async index({ auth, response }: HttpContext) {
    try {
      // Only superadmin and admin can list tenants
      const currentUser = auth.getUserOrFail()
      if (!['superadmin', 'admin'].includes(currentUser.role)) {
        return response.forbidden({
          message: 'Insufficient permissions to list tenants'
        })
      }

      const tenants = await Tenant.all()

      return response.ok({
        message: 'Tenants retrieved successfully',
        data: {
          tenants: tenants.map(tenant => ({
            id: tenant.id,
            name: tenant.name,
            slug: tenant.slug,
            dbHost: tenant.dbHost,
            dbPort: tenant.dbPort,
            dbName: tenant.dbName,
            dbUser: tenant.dbUser,
            status: tenant.status
          }))
        }
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to retrieve tenants',
        error: error.message
      })
    }
  }

  /**
   * @show
   * @summary Get tenant by slug
   * @description Retrieve a specific tenant by its slug (superadmin and admin only)
   * @tag Tenant Management
   * @paramPath slug - The unique slug of the tenant - @type(string) @required
   * @responseBody 200 - {"message": "Tenant retrieved successfully", "data": {"tenant": {"id": "uuid", "name": "Acme Corp", "slug": "acme-corp", "dbHost": "localhost", "dbPort": 5432, "dbName": "acme_corp_db", "dbUser": "acme_user", "status": "active"}}}
   * @responseBody 403 - {"message": "Insufficient permissions to view tenant details"}
   * @responseBody 404 - {"message": "Tenant not found", "error": "No tenant found with the given slug"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async show({ params, auth, response }: HttpContext) {
    try {
      // Only superadmin and admin can view tenant details
      const currentUser = auth.getUserOrFail()
      if (!['superadmin', 'admin'].includes(currentUser.role)) {
        return response.forbidden({
          message: 'Insufficient permissions to view tenant details'
        })
      }

      const tenant = await Tenant.findByOrFail('slug', params.slug)

      return response.ok({
        message: 'Tenant retrieved successfully',
        data: {
          tenant: {
            id: tenant.id,
            name: tenant.name,
            slug: tenant.slug,
            dbHost: tenant.dbHost,
            dbPort: tenant.dbPort,
            dbName: tenant.dbName,
            dbUser: tenant.dbUser,
            status: tenant.status
          }
        }
      })
    } catch (error) {
      return response.notFound({
        message: 'Tenant not found',
        error: error.message
      })
    }
  }

  /**
   * @update
   * @summary Update tenant
   * @description Update tenant metadata (superadmin only) - does not modify database structure
   * @tag Tenant Management
   * @paramPath slug - The unique slug of the tenant - @type(string) @required
   * @requestBody {"name": "Updated Acme Corp", "status": "inactive"}
   * @responseBody 200 - {"message": "Tenant updated successfully", "data": {"tenant": {"id": "uuid", "name": "Updated Acme Corp", "slug": "acme-corp", "dbHost": "localhost", "dbPort": 5432, "dbName": "acme_corp_db", "dbUser": "acme_user", "status": "inactive"}}}
   * @responseBody 403 - {"message": "Only superadmin can update tenants"}
   * @responseBody 404 - {"message": "Tenant not found"}
   * @responseBody 422 - {"message": "Validation failed", "errors": [{"field": "status", "message": "Invalid status value"}]}
   * @responseBody 400 - {"message": "Failed to update tenant", "error": "Update operation failed"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async update({ params, request, auth, response }: HttpContext) {
    try {
      // Only superadmin can update tenants
      const currentUser = auth.getUserOrFail()
      if (currentUser.role !== 'superadmin') {
        return response.forbidden({
          message: 'Only superadmin can update tenants'
        })
      }

      const tenant = await Tenant.findByOrFail('slug', params.slug)
      const updateData = await request.validateUsing(updateTenantValidator)

      tenant.merge(updateData)
      await tenant.save()

      return response.ok({
        message: 'Tenant updated successfully',
        data: {
          tenant: {
            id: tenant.id,
            name: tenant.name,
            slug: tenant.slug,
            dbHost: tenant.dbHost,
            dbPort: tenant.dbPort,
            dbName: tenant.dbName,
            dbUser: tenant.dbUser,
            status: tenant.status
          }
        }
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to update tenant',
        error: error.message
      })
    }
  }

  /**
   * @destroy
   * @summary Delete tenant
   * @description Delete a tenant and its database - USE WITH CAUTION! (superadmin only)
   * @tag Tenant Management
   * @paramPath slug - The unique slug of the tenant - @type(string) @required
   * @responseBody 200 - {"message": "Tenant deleted successfully"}
   * @responseBody 403 - {"message": "Only superadmin can delete tenants"}
   * @responseBody 404 - {"message": "Tenant not found"}
   * @responseBody 400 - {"message": "Failed to delete tenant", "error": "Database deletion failed"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async destroy({ params, auth, response }: HttpContext) {
    try {
      // Only superadmin can delete tenants
      const currentUser = auth.getUserOrFail()
      if (currentUser.role !== 'superadmin') {
        return response.forbidden({
          message: 'Only superadmin can delete tenants'
        })
      }

      // Remove tenant and its database
      await TenantOnboardingService.removeTenant(params.slug)

      return response.ok({
        message: 'Tenant deleted successfully'
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to delete tenant',
        error: error.message
      })
    }
  }
}
