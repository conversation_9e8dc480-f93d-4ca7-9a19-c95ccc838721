/**
 * Common API response interfaces for Swagger documentation
 */

export interface ApiResponse<T = any> {
  message: string
  data?: T
  error?: string
}

export interface ValidationError {
  field: string
  message: string
  rule?: string
}

export interface ValidationErrorResponse {
  message: string
  errors: ValidationError[]
}

export interface PaginatedResponse<T = any> {
  message: string
  data: {
    items: T[]
    meta: {
      total: number
      perPage: number
      currentPage: number
      lastPage: number
      firstPage: number
      firstPageUrl: string
      lastPageUrl: string
      nextPageUrl: string | null
      previousPageUrl: string | null
    }
  }
}

// System User related interfaces
export interface SystemUser {
  id: string
  email: string
  name: string
  role: 'superadmin' | 'admin' | 'support'
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  message: string
  data: {
    user: SystemUser
    token: {
      type: 'bearer'
      value: string
      expiresAt: string
    }
  }
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  password_confirmation: string
  role?: 'superadmin' | 'admin' | 'support'
}

export interface AccessToken {
  id: string
  name: string
  abilities: string[]
  createdAt: string
  expiresAt: string | null
  lastUsedAt: string | null
}

export interface CreateTokenRequest {
  name?: string
  abilities?: string[]
  expiresIn?: string
}

export interface CreateTokenResponse {
  message: string
  data: {
    token: {
      type: 'bearer'
      value: string
      name: string
      abilities: string[]
      expiresAt: string | null
    }
  }
}

// Tenant related interfaces
export interface Tenant {
  id: string
  name: string
  slug: string
  dbHost: string
  dbPort: number
  dbName: string
  dbUser: string
  status: 'active' | 'inactive'
  createdAt?: string
  updatedAt?: string
}

export interface CreateTenantRequest {
  name: string
  slug: string
  dbHost?: string
  dbPort?: number
  dbName?: string
  dbUser?: string
  dbPassword?: string
  status?: 'active' | 'inactive'
}

export interface UpdateTenantRequest {
  name?: string
  status?: 'active' | 'inactive'
}

export interface TenantResponse {
  message: string
  data: {
    tenant: Tenant
  }
}

export interface TenantsListResponse {
  message: string
  data: {
    tenants: Tenant[]
  }
}

// Health check interfaces
export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'warning'
  message?: string
}

export interface HealthCheckWithData<T> extends HealthCheck {
  data?: T
}

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  checks: {
    database: HealthCheck
    memory: HealthCheckWithData<{ usage: number; max: number }>
    uptime: HealthCheckWithData<{ seconds: number }>
  }
}

export interface LivenessResponse {
  status: 'healthy'
  timestamp: string
}

export interface ReadinessResponse {
  status: 'healthy' | 'unhealthy'
  timestamp: string
}

// Error response interfaces
export interface UnauthorizedResponse {
  message: string
  error?: string
}

export interface ForbiddenResponse {
  message: string
}

export interface NotFoundResponse {
  message: string
  error?: string
}

export interface BadRequestResponse {
  message: string
  error?: string
}

export interface InternalServerErrorResponse {
  message: string
  error?: string
}
