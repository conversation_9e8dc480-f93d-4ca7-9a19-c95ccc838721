import type { HttpContext } from '@adonisjs/core/http'
import HealthService from '#services/health_service'

export default class HealthController {
  private healthService = new HealthService()

  /**
   * @check
   * @summary Comprehensive health check
   * @description Performs a comprehensive health check including database connectivity, memory usage, and uptime
   * @tag Health
   * @responseBody 200 - {"status": "healthy", "timestamp": "2024-01-01T00:00:00.000Z", "checks": {"database": {"status": "healthy"}, "memory": {"status": "healthy", "data": {"usage": 50.5, "max": 100.0}}, "uptime": {"status": "healthy", "data": {"seconds": 3600}}}}
   * @responseBody 503 - {"status": "unhealthy", "timestamp": "2024-01-01T00:00:00.000Z", "checks": {"database": {"status": "unhealthy", "message": "Connection failed"}, "memory": {"status": "warning", "data": {"usage": 95.0, "max": 100.0}}, "uptime": {"status": "healthy", "data": {"seconds": 3600}}}}
   */
  public async check({ response }: HttpContext) {
    const health = await this.healthService.check()

    return response.status(health.status === 'healthy' ? 200 : 503).json(health)
  }

  /**
   * @liveness
   * @summary Liveness probe
   * @description Simple liveness probe to check if the application is responding
   * @tag Health
   * @responseBody 200 - {"status": "healthy", "timestamp": "2024-01-01T00:00:00.000Z"}
   */
  public async liveness({ response }: HttpContext) {
    // Simple liveness probe - just check if app is responding
    return response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
    })
  }

  /**
   * @readiness
   * @summary Readiness probe
   * @description Readiness probe to check if the application is ready to serve traffic by checking dependencies
   * @tag Health
   * @responseBody 200 - {"status": "healthy", "timestamp": "2024-01-01T00:00:00.000Z"}
   * @responseBody 503 - {"status": "unhealthy", "timestamp": "2024-01-01T00:00:00.000Z"}
   */
  public async readiness({ response }: HttpContext) {
    // Readiness probe - check dependencies
    const health = await this.healthService.check()

    return response.status(health.status === 'healthy' ? 200 : 503).json({
      status: health.status,
      timestamp: health.timestamp,
    })
  }
}