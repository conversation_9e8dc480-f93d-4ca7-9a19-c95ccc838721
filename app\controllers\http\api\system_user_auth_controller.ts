import type { HttpContext } from '@adonisjs/core/http'
import SystemUserModel from '#models/system_user_model'
import { loginValidator, registerValidator } from '#validators/system_user_auth_validator'

export default class SystemUserAuthController {
  /**
   * @login
   * @summary System user login
   * @description Authenticate a system user and return an access token
   * @tag System User Authentication
   * @requestBody {"email": "<EMAIL>", "password": "password123"}
   * @responseBody 200 - {"message": "Login successful", "data": {"user": {"id": "uuid", "email": "<EMAIL>", "name": "Admin User", "role": "admin"}, "token": {"type": "bearer", "value": "oat_xxx", "expiresAt": "2024-01-02T00:00:00.000Z"}}}
   * @responseBody 401 - {"message": "Invalid credentials", "error": "Authentication failed"}
   * @responseBody 422 - {"message": "Valida<PERSON> failed", "errors": [{"field": "email", "message": "<PERSON>ail is required"}]}
   */
  async login({ request, auth, response }: HttpContext) {
    try {
      // Validate the request data
      const { email, password } = await request.validateUsing(loginValidator)

      // Verify user credentials using master database connection
      const user = await SystemUserModel.verifyCredentials(email, password)

      // Create an access token for the user
      const token = await auth.use('system_user').createToken(user, ['*'], {
        name: 'System User Login Token',
        expiresIn: '24 hours'
      })

      return response.ok({
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          },
          token: {
            type: 'bearer',
            value: token.value!.release(),
            expiresAt: token.expiresAt
          }
        }
      })
    } catch (error) {
      return response.unauthorized({
        message: 'Invalid credentials',
        error: error.message
      })
    }
  }

  /**
   * @register
   * @summary Register a new system user
   * @description Create a new system user (only superadmin can perform this action)
   * @tag System User Authentication
   * @requestBody {"name": "New Admin", "email": "<EMAIL>", "password": "password123", "password_confirmation": "password123", "role": "admin"}
   * @responseBody 201 - {"message": "System user created successfully", "data": {"user": {"id": "uuid", "email": "<EMAIL>", "name": "New Admin", "role": "admin"}}}
   * @responseBody 403 - {"message": "Only superadmin can create new system users"}
   * @responseBody 422 - {"message": "Validation failed", "errors": [{"field": "email", "message": "Email already exists"}]}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async register({ request, auth, response }: HttpContext) {
    try {
      // Only superadmin can create new system users
      const currentUser = auth.getUserOrFail()
      if (currentUser.role !== 'superadmin') {
        return response.forbidden({
          message: 'Only superadmin can create new system users'
        })
      }

      // Validate the request data
      const userData = await request.validateUsing(registerValidator)

      // Create the new system user
      const user = await SystemUserModel.create(userData)

      return response.created({
        message: 'System user created successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          }
        }
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to create system user',
        error: error.message
      })
    }
  }

  /**
   * @profile
   * @summary Get current user profile
   * @description Retrieve the profile information of the currently authenticated system user
   * @tag System User Authentication
   * @responseBody 200 - {"message": "Profile retrieved successfully", "data": {"user": {"id": "uuid", "email": "<EMAIL>", "name": "Admin User", "role": "admin", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}}}
   * @responseBody 401 - {"message": "User not authenticated", "error": "Authentication required"}
   */
  async profile({ auth, response }: HttpContext) {
    try {
      const user = auth.getUserOrFail()

      return response.ok({
        message: 'Profile retrieved successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
          }
        }
      })
    } catch (error) {
      return response.unauthorized({
        message: 'User not authenticated',
        error: error.message
      })
    }
  }

  /**
   * @logout
   * @summary Logout current user
   * @description Logout the current user by revoking the current access token
   * @tag System User Authentication
   * @responseBody 200 - {"message": "Logout successful"}
   * @responseBody 400 - {"message": "Failed to logout", "error": "Token revocation failed"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async logout({ auth, response }: HttpContext) {
    try {
      const user = auth.getUserOrFail()
      const token = auth.user?.currentAccessToken

      if (token) {
        await SystemUserModel.accessTokens.delete(user, token.identifier)
      }

      return response.ok({
        message: 'Logout successful'
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to logout',
        error: error.message
      })
    }
  }

  /**
   * @tokens
   * @summary List user access tokens
   * @description Get all access tokens for the current authenticated user
   * @tag System User Authentication
   * @responseBody 200 - {"message": "Tokens retrieved successfully", "data": {"tokens": [{"id": "token-id", "name": "API Token", "abilities": ["*"], "createdAt": "2024-01-01T00:00:00.000Z", "expiresAt": "2024-01-31T00:00:00.000Z", "lastUsedAt": null}]}}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async tokens({ auth, response }: HttpContext) {
    try {
      const user = auth.getUserOrFail()
      const tokens = await SystemUserModel.accessTokens.all(user)

      return response.ok({
        message: 'Tokens retrieved successfully',
        data: {
          tokens: tokens.map(token => ({
            id: token.identifier,
            name: token.name,
            abilities: token.abilities,
            createdAt: token.createdAt,
            updatedAt: token.updatedAt,
            expiresAt: token.expiresAt,
            lastUsedAt: token.lastUsedAt
          }))
        }
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to retrieve tokens',
        error: error.message
      })
    }
  }

  /**
   * @deleteToken
   * @summary Delete access token
   * @description Delete a specific access token by its ID
   * @tag System User Authentication
   * @paramPath tokenId - The ID of the token to delete - @type(string) @required
   * @responseBody 200 - {"message": "Token deleted successfully"}
   * @responseBody 400 - {"message": "Failed to delete token", "error": "Token not found"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async deleteToken({ auth, params, response }: HttpContext) {
    try {
      const user = auth.getUserOrFail()
      await SystemUserModel.accessTokens.delete(user, params.tokenId)

      return response.ok({
        message: 'Token deleted successfully'
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to delete token',
        error: error.message
      })
    }
  }

  /**
   * @createToken
   * @summary Create new access token
   * @description Create a new access token for the current authenticated user
   * @tag System User Authentication
   * @requestBody {"name": "API Token", "abilities": ["*"], "expiresIn": "30 days"}
   * @responseBody 201 - {"message": "Token created successfully", "data": {"token": {"type": "bearer", "value": "oat_xxx", "name": "API Token", "abilities": ["*"], "expiresAt": "2024-01-31T00:00:00.000Z"}}}
   * @responseBody 400 - {"message": "Failed to create token", "error": "Invalid request"}
   * @responseBody 401 - {"message": "Authentication required"}
   */
  async createToken({ auth, request, response }: HttpContext) {
    try {
      const user = auth.getUserOrFail()
      const { name, abilities = ['*'], expiresIn = '30 days' } = request.only(['name', 'abilities', 'expiresIn'])

      const token = await SystemUserModel.accessTokens.create(user, abilities, {
        name: name || 'API Token',
        expiresIn
      })

      return response.created({
        message: 'Token created successfully',
        data: {
          token: {
            type: 'bearer',
            value: token.value!.release(),
            name: token.name,
            abilities: token.abilities,
            expiresAt: token.expiresAt
          }
        }
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to create token',
        error: error.message
      })
    }
  }
}