import path from "node:path";
import url from "node:url";

export default {
  // Path to the application root
  path: path.dirname(url.fileURLToPath(import.meta.url)) + "/../",
  
  // API Information
  info: {
    title: "AI Studio API",
    version: "1.0.0",
    description: "Multi-tenant AI Studio API with system user authentication and tenant management",
    contact: {
      name: "AI Studio Team",
      email: "<EMAIL>"
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT"
    }
  },

  // Swagger UI Configuration
  tagIndex: 3, // For routes like /api/system/auth/... tag will be 'system'
  productionEnv: "production",
  snakeCase: true,
  debug: false,
  
  // Routes to ignore in documentation
  ignore: ["/swagger", "/docs", "/"],
  
  // Prefer PUT over PATCH when both are available
  preferredPutPatch: "PUT",
  
  // Common parameters and headers used across endpoints
  common: {
    parameters: {
      // Pagination parameters
      pagination: [
        {
          in: "query",
          name: "page",
          schema: { type: "integer", minimum: 1, default: 1 },
          description: "Page number for pagination"
        },
        {
          in: "query",
          name: "limit",
          schema: { type: "integer", minimum: 1, maximum: 100, default: 20 },
          description: "Number of items per page"
        }
      ],
      // Sorting parameters
      sorting: [
        {
          in: "query",
          name: "sortBy",
          schema: { type: "string" },
          description: "Field to sort by"
        },
        {
          in: "query",
          name: "sortOrder",
          schema: { type: "string", enum: ["asc", "desc"], default: "asc" },
          description: "Sort order"
        }
      ]
    },
    headers: {
      // Standard response headers
      standard: {
        "X-Request-ID": {
          description: "Unique request identifier",
          schema: { type: "string", format: "uuid" }
        },
        "X-Rate-Limit-Remaining": {
          description: "Number of requests remaining in the current window",
          schema: { type: "integer" }
        }
      },
      // Pagination headers
      paginated: {
        "X-Total-Count": {
          description: "Total number of items",
          schema: { type: "integer" }
        },
        "X-Page-Count": {
          description: "Total number of pages",
          schema: { type: "integer" }
        },
        "X-Current-Page": {
          description: "Current page number",
          schema: { type: "integer" }
        },
        "X-Per-Page": {
          description: "Items per page",
          schema: { type: "integer" }
        }
      }
    }
  },

  // Security schemes for authentication
  securitySchemes: {
    BearerAuth: {
      type: "http",
      scheme: "bearer",
      bearerFormat: "JWT",
      description: "System user access token authentication"
    },
    ApiKeyAuth: {
      type: "apiKey",
      in: "header",
      name: "X-API-Key",
      description: "API key authentication"
    }
  },

  // Middleware names that indicate authentication is required
  authMiddlewares: ["auth", "auth:system_user"],
  
  // Default security scheme to use
  defaultSecurityScheme: "BearerAuth",
  
  // Persist authorization between page reloads
  persistAuthorization: true,
  
  // Show full path in endpoint summary
  showFullPath: false,

  // Servers configuration
  servers: [
    {
      url: "http://localhost:3333",
      description: "Development server"
    },
    {
      url: "https://api.ai-studio.com",
      description: "Production server"
    }
  ],

  // External documentation
  externalDocs: {
    description: "Find more info here",
    url: "https://docs.ai-studio.com"
  }
};
